# ============================================================================
# WIZ AROMA DELIVERY SYSTEM - ENVIRONMENT CONFIGURATION
# ============================================================================
# This file contains all environment variables for the Wiz Aroma delivery system.
# Replace placeholder values with your actual credentials and configuration.

# ============================================================================
# BOT TOKENS - Telegram Bot API Tokens from @BotFather
# ============================================================================
BOT_TOKEN=7575817388:AAF1CPOYFBKkD23t8pkKb3n6loYvk6i-9xM                    # Main customer-facing bot
ADMIN_BOT_TOKEN=7228244635:AAF9oe7R-rMZCPv2B2hoIpkQN4o8L8_x8AE               # Administrative management bot
FINANCE_BOT_TOKEN=7718886259:AAHB5jHel5V9MTEuqikNtxb3-ftg4WikzHE             # Financial operations bot
MAINTENANCE_BOT_TOKEN=8031233311:AAE26-PP45S7N959ACw1kVV8s_CqYbVdGew         # System maintenance bot
MANAGEMENT_BOT_TOKEN=7809065144:AAHaCCfC3NP_-smweC5nl4YRa5VZZkWIKgw          # Management dashboard bot
ORDER_TRACK_BOT_TOKEN=8123775284:AAGLvVRpVenfI5EjA5TnZnUcz0mz_eGoV20        # Order tracking bot
DELIVERY_BOT_TOKEN=7761341183:AAEyWv6IQJ2pQ5yuyWrVCd38Ft48UK_jVm8           # Delivery personnel bot

# ============================================================================
# AUTHORIZED USER IDs - Telegram User IDs for Bot Access Control
# ============================================================================
# System Administrator (Full Access)
SYSTEM_ADMIN_ID=7729984017

# Bot-Specific Authorized Users
ADMIN_CHAT_IDS="[940828137]"                                     # Admin bot access
FINANCE_CHAT_ID=940828137                                        # Finance bot access
MAINTENANCE_CHAT_ID=940828137,7729984017                                   # Maintenance bot access
ORDER_TRACK_BOT_AUTHORIZED_IDS="[940828137]"                     # Order tracking bot access
DELIVERY_BOT_AUTHORIZED_IDS="[940828137]"                        # Delivery bot access
MANAGEMENT_BOT_AUTHORIZED_IDS="[940828137,7729984017]"                      # Management bot access

# ============================================================================
# EMAIL CONFIGURATION - SMTP Settings for Notifications
# ============================================================================
EMAIL_ADDRESS=*******                                     # System email address
EMAIL_PASSWORD=styq jpue krup xixw                                           # App-specific password

# ============================================================================
# PAYMENT CONFIGURATION - Customer Payment Methods
# ============================================================================
# Telebirr Mobile Money
TELEBIRR_PHONE=**********                                                    # Telebirr phone number
TELEBIRR_NAME=Wiz Aroma                                                      # Account holder name

# Commercial Bank of Ethiopia (CBE)
CBE_ACCOUNT_NUMBER=*************                                             # CBE account number
CBE_ACCOUNT_NAME=Wiz Aroma                                                   # CBE account holder name

# Bank of Abyssinia (BOA)
BOA_ACCOUNT_NUMBER=********                                                  # BOA account number
BOA_ACCOUNT_NAME=Wiz Aroma                                                   # BOA account holder name

# ============================================================================
# CONTACT INFORMATION - Customer Support Details
# ============================================================================
# Primary Support Contacts
SUPPORT_PHONE_1=**********                                                   # Primary support phone
SUPPORT_PHONE_2=**********                                                   # Secondary support phone
SUPPORT_TELEGRAM=@wiz_aroma_contact_center                                   # Telegram support channel

# Customer Service
CUSTOMER_SERVICE_PHONE=**********                                            # Customer service phone
CUSTOMER_SERVICE_EMAIL=*******                                  # Customer service email
BUSINESS_EMAIL=*******                                    # Main business email

# ============================================================================
# FIREBASE CONFIGURATION - Database and Authentication
# ============================================================================
# Firebase Realtime Database URL
FIREBASE_DATABASE_URL=https://custom-domain-email-service-default-rtdb.firebaseio.com

# Firebase Service Account Credentials (JSON format for secure deployment)
# Note: Using environment variable instead of credential files for enhanced security
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
# ============================================================================
# APPLICATION CONFIGURATION - System Settings
# ============================================================================
TEST_MODE=False                                                              # Enable/disable test mode
LOG_LEVEL=INFO                                                               # Logging level (DEBUG, INFO, WARNING, ERROR)

# ============================================================================
# END OF CONFIGURATION
# ============================================================================